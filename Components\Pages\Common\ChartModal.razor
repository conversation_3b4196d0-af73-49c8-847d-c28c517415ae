@using ApexCharts
@using System.Linq

<!-- 互動視窗按鈕 -->
<div class="text-center">
    <button class="btn btn-success"
            data-bs-toggle="modal"
            data-bs-target="@ModalId">
        <i class="fa-solid fa-magnifying-glass-plus"></i> 查看詳細圖表
    </button>
</div>

<!-- Bootstrap 5 互動視窗 -->
<div class="modal fade" id="@ModalId" tabindex="-1" aria-labelledby="@($"{ModalId}Label")" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="@($"{ModalId}Label")">
                    <i class="fa-solid fa-chart-line"></i> @Title
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 互動視窗內的圖表 -->
                <div>
                    @if (shouldRenderModalChart)
                    {
                        @if (ChartData != null && ChartData.Any())
                        {
                            <ApexChart TItem="TItem"
                                       Title="@ChartTitle"
                                       Options="ChartOptions">
                                <ApexPointSeries TItem="TItem"
                                                 Items="ChartData"
                                                 SeriesType="CurrentChartType"
                                                 Name="@SeriesName"
                                                 XValue="XValueSelector"
                                                 YValue="YValueSelector" />
                            </ApexChart>
                        }
                        else if (ChildContent != null)
                        {
                            @ChildContent
                        }
                    }
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
            </div>
        </div>
    </div>
</div>

@code {
    @typeparam TItem where TItem : class

    [Parameter] public string ModalId { get; set; } = "chartModal";
    [Parameter] public string Title { get; set; } = "詳細圖表";
    [Parameter] public string ChartTitle { get; set; } = "圖表詳細檢視";
    [Parameter] public string SeriesName { get; set; } = "數據";

    // 圖表相關參數
    [Parameter] public List<TItem>? ChartData { get; set; }
    [Parameter] public ApexChartOptions<TItem>? ChartOptions { get; set; }
    [Parameter] public SeriesType CurrentChartType { get; set; } = SeriesType.Bar;
    [Parameter] public Func<TItem, object>? XValueSelector { get; set; }
    [Parameter] public Func<TItem, decimal?>? YValueSelector { get; set; }

    // 自定義內容參數
    [Parameter] public RenderFragment? ChildContent { get; set; }
   
    
    // 控制渲染
    private bool shouldRenderModalChart = true;

    protected override void OnParametersSet()
    {
        // 當參數變更時重新渲染圖表
        shouldRenderModalChart = false;
        StateHasChanged();
        
        Task.Delay(50).ContinueWith(_ =>
        {
            InvokeAsync(() =>
            {
                shouldRenderModalChart = true;
                StateHasChanged();
            });
        });
    }

    private string FormatCellValue(object value)
    {
        if (value == null) return "";
        
        if (value is decimal decimalValue)
        {
            return decimalValue.ToString("C0");
        }
        
        return value.ToString() ?? "";
    }
}
