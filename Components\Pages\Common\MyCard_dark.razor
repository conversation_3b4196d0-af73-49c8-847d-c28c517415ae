@inject IConfiguration Configuration
@inject IJSRuntime JS
@rendermode InteractiveServer

<div class="card-container h-100">
    <div class="card @Css h-100">
        <div class="card-header d-flex justify-content-between align-items-center mt-2">
            <div class="d-flex align-items-center gap-2">
                <span class="badge bg-secondary fw-bold fs-5">@Year</span>
                <span class="fw-bold fs-5">@Title</span>
            </div>

            @if (IsModal)
            {
                <div>
                    @* 互動視窗表格 *@
                    <BS5Modal Id="@ModalId" Title="@($"{Title} - 數據表格")">
                        <ButtonContent>
                            <i class="fa-solid fa-table"></i>
                        </ButtonContent>

                        <ChildContent>
                            <TableExcel TableColumns="@TableColumns" TableData="@TableData" />
                        </ChildContent>
                    </BS5Modal>

                    @* 下載 *@
                    @if (!string.IsNullOrEmpty(ExcelUrl))
                    {
                        <a href="@GetDownloadUrl()" class="ms-2" download>
                            <i class="fa-solid fa-download"></i>
                        </a>
                    }
                    else if (OnExportExcel.HasDelegate)
                    {
                        <a href="javascript:void(0)" class="ms-2" @onclick="OnExportExcel" @onclick:preventDefault="true" title="匯出Excel">
                            <i class="fa-solid fa-download"></i>
                        </a>
                    }
                </div>
            }

        </div>
        <div class="card-body @bodyCss">
             @ChildContent
        </div>
    </div>
</div>


@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public RenderFragment? ChartContent { get; set; }
    [Parameter] public string? Title { get; set; }
    [Parameter] public string? Year { get; set; }
    [Parameter] public string? Css { get; set; }
    [Parameter] public string? bodyCss { get; set; }
    [Parameter] public bool IsModal { get; set; } = false;
    [Parameter] public string? ExcelUrl { get; set; }
    [Parameter] public string ModalId { get; set; } = "myModal";
    [Parameter] public EventCallback OnExportExcel { get; set; }
    [Parameter] public List<string>? TableColumns { get; set; }

    /// <summary>
    /// TableData 為 Dictionary 的 List，每筆 Dictionary 表示一列資料，key 是欄位名稱
    /// </summary>
    [Parameter] public List<Dictionary<string, object>>? TableData { get; set; }

    // 統計資訊結構
    public class ColumnStats
    {
        public decimal Max { get; set; }
        public decimal Min { get; set; }
        public decimal Average { get; set; }
        public decimal Sum { get; set; }
    }

    // 獲取數值型欄位
    private List<string> GetNumericColumns()
    {
        if (TableData == null || !TableData.Any() || TableColumns == null)
            return new List<string>();

        var numericColumns = new List<string>();
        var firstRow = TableData.First();

        foreach (var column in TableColumns)
        {
            if (firstRow.ContainsKey(column) && firstRow[column] != null)
            {
                var value = firstRow[column];
                if (IsNumericValue(value))
                {
                    numericColumns.Add(column);
                }
            }
        }

        return numericColumns;
    }

    // 檢查值是否為數值型
    private bool IsNumericValue(object value)
    {
        if (value == null) return false;

        return value is int || value is long || value is decimal ||
               value is double || value is float ||
               (decimal.TryParse(value.ToString(), out _));
    }

    // 計算欄位統計資訊
    private ColumnStats CalculateColumnStats(string columnName)
    {
        var stats = new ColumnStats();

        if (TableData == null || !TableData.Any())
            return stats;

        var values = new List<decimal>();

        foreach (var row in TableData)
        {
            if (row.ContainsKey(columnName) && row[columnName] != null)
            {
                if (decimal.TryParse(row[columnName].ToString(), out decimal value))
                {
                    values.Add(value);
                }
            }
        }

        if (values.Any())
        {
            stats.Max = values.Max();
            stats.Min = values.Min();
            stats.Average = values.Average();
            stats.Sum = values.Sum();
        }

        return stats;
    }

    /// <summary>
    /// 取得下載 URL，在正式環境時加上 Url 前綴
    /// </summary>
    private string GetDownloadUrl()
    {
        string apiUrl = Configuration["SysSetting:DownLoadUrl"] ?? "";
        string downloadUrl = $"{apiUrl}/Excel/download?url={Uri.EscapeDataString(ExcelUrl ?? "")}";

        return downloadUrl;
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
    }
}
