# 互動視窗統計圖表功能實現說明

## 功能概述
實現了一個互動視窗功能，讓使用者可以點擊按鈕查看詳細的統計圖表。此功能整合了 ChartModal 組件到 MyCard_dark2 中，並修改了 AzureChart 組件以支援互動視窗顯示。

## 修改的文件

### 1. Components/Pages/Common/ChartModal.razor
- **修改內容**：
  - 添加了 `ChildContent` 參數支援自定義內容
  - 修改了 modal-body 邏輯，當沒有 ChartData 時顯示 ChildContent
  - 保持了原有的圖表顯示功能

- **新增功能**：
  ```razor
  [Parameter] public RenderFragment? ChildContent { get; set; }
  ```

### 2. Components/Pages/Common/MyCard_dark2.razor
- **修改內容**：
  - 在互動視窗圖表區域添加了 ChartModal 組件
  - 使用 `ModalChartContent` 參數來傳遞圖表內容
  - 設定了適當的 ModalId 以避免衝突

- **新增代碼**：
  ```razor
  <!-- 互動視窗圖表 -->
  @if (ModalChartContent != null)
  {
      <ChartModal TItem="Dictionary<string, object>"
                  ModalId="@($"{ModalId}_chart")"
                  Title="@($"{Title} - 詳細圖表")"
                  ChartTitle="@Title"
                  SeriesName="數據">
          @ModalChartContent
      </ChartModal>
  }
  ```

### 3. Components/Pages/MyChart/AzureChart.razor
- **修改內容**：
  - 添加了 `ModalChartContent` 屬性，自動生成互動視窗圖表內容
  - 實現了 `CreateModalChartContent()` 方法，動態創建圖表組件
  - 將 ModalChartContent 傳遞給 MyCard_dark2 組件

- **新增功能**：
  ```csharp
  // 互動視窗圖表內容
  private RenderFragment? ModalChartContent => CreateModalChartContent();
  
  // 創建互動視窗圖表內容的方法
  private RenderFragment? CreateModalChartContent() { ... }
  ```

### 4. Components/Pages/AzureCosmos.razor
- **修改內容**：
  - 移除了手動創建的 BS5Modal 互動視窗代碼
  - 現在所有的 AzureChart 組件都自動包含互動視窗功能

## 使用方式

### 自動功能
所有使用 `AzureChart` 組件的地方都會自動獲得互動視窗功能：
- 每個圖表卡片會顯示一個 "查看詳細圖表" 按鈕
- 點擊按鈕會開啟互動視窗，顯示相同的統計圖表
- 互動視窗中的圖表與原始圖表使用相同的數據和配置

### 手動使用 ChartModal
如果需要在其他地方使用 ChartModal：

```razor
<ChartModal TItem="YourDataType"
            ModalId="uniqueModalId"
            Title="圖表標題"
            ChartTitle="詳細圖表標題"
            SeriesName="數據系列名稱"
            ChartData="@yourChartData"
            ChartOptions="@yourChartOptions"
            CurrentChartType="SeriesType.Bar"
            XValueSelector="@(x => x.XProperty)"
            YValueSelector="@(x => x.YProperty)">
</ChartModal>
```

或使用自定義內容：

```razor
<ChartModal TItem="Dictionary<string, object>"
            ModalId="customModalId"
            Title="自定義標題">
    <!-- 自定義內容 -->
    <div>您的自定義圖表或內容</div>
</ChartModal>
```

## 技術特點

1. **自動化**：AzureChart 組件自動生成互動視窗內容，無需手動配置
2. **靈活性**：ChartModal 支援兩種模式 - 標準圖表模式和自定義內容模式
3. **一致性**：互動視窗中的圖表與原始圖表保持完全一致的數據和樣式
4. **可重用性**：ChartModal 組件可以在任何地方重複使用

## 測試建議

1. 檢查所有 AzureChart 組件是否都顯示了互動視窗按鈕
2. 點擊按鈕確認互動視窗正常開啟
3. 驗證互動視窗中的圖表數據與原始圖表一致
4. 測試多個圖表的互動視窗是否會相互干擾（應該不會，因為使用了唯一的 ModalId）
